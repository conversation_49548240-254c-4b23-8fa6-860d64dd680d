#!/usr/bin/env python3
"""
Test script to verify the candle callback mechanism is working
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from utils.enhanced_websocket_service import EnhancedWebSocketService

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test callback function
async def test_callback(candle_data):
    """Test callback function to verify callbacks are working"""
    logger.info(f"🎯 [TEST_CALLBACK] Received candle: {candle_data['symbol']} {candle_data['timeframe']} at {candle_data['timestamp']}")
    logger.info(f"🎯 [TEST_CALLBACK] OHLCV: O={candle_data['open']:.2f} H={candle_data['high']:.2f} L={candle_data['low']:.2f} C={candle_data['close']:.2f} V={candle_data['volume']}")

async def main():
    """Main test function"""
    logger.info("🚀 Starting callback test...")
    
    # Create enhanced websocket service
    service = EnhancedWebSocketService()
    
    # Register test callback
    service.add_candle_callback(test_callback)
    logger.info("✅ Test callback registered")
    
    # Start the service
    await service.start()
    logger.info("✅ Enhanced WebSocket service started")
    
    # Let it run for a few minutes to see if callbacks are triggered
    logger.info("⏳ Waiting for candle callbacks... (will run for 5 minutes)")
    await asyncio.sleep(300)  # 5 minutes
    
    # Stop the service
    await service.stop()
    logger.info("🛑 Test completed")

if __name__ == "__main__":
    asyncio.run(main())
